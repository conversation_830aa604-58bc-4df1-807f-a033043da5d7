package id.co.bri.brimo;

import android.app.Application;
import android.content.Context;
import android.content.res.Configuration;
import android.os.Build;
import android.util.Log;
import android.webkit.WebView;

import com.airbnb.android.react.lottie.LottiePackage;
import com.brimodsdk.RNBrimodSDK;
import com.chuckerteam.chucker.api.Chucker;
import com.chuckerteam.chucker.api.ChuckerCollector;

// React Native imports
import com.dieam.reactnativepushnotification.ReactNativePushNotificationPackage;
import com.facebook.react.ReactApplication;
import com.facebook.react.ReactNativeHost;
import com.facebook.react.ReactPackage;
import com.facebook.react.shell.MainReactPackage;

import com.facebook.soloader.SoLoader;

import java.util.Arrays;
import java.util.List;

import com.jakewharton.threetenabp.AndroidThreeTen;

import androidx.appcompat.app.AppCompatDelegate;

import java.util.Locale;

import id.co.bri.brimo.di.components.ApplicationComponent;
import id.co.bri.brimo.di.components.DaggerApplicationComponent;
import id.co.bri.brimo.di.modules.ApiModule;
import id.co.bri.brimo.di.modules.ApplicationModule;
import id.co.bri.brimo.di.modules.MinioModule;
import id.co.bri.brimo.di.modules.fragment.RoomPengelolaanKartuModule;
import id.co.bri.brimo.di.modules.singalarity.DynamicKeyModule;
import id.co.bri.brimo.di.modules.RoomLifestyleModule;
import id.co.bri.brimo.di.modules.RoomMenuModule;
import id.co.bri.brimo.di.modules.RoomModule;
import id.co.bri.brimo.di.modules.RoomRateModule;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.payment.PaymentImpl;
import id.co.bri.brimo.payment.dependency.PaymentDependency;
import id.co.bri.brimo.reactnative.BrimodSDK;
import id.co.bri.brimo.util.ota.DownloadManager;
import id.co.bri.brimo.util.AppIconManager;
import id.co.bri.brimo.util.AppsFlyerUtil;
import id.co.bri.brimo.util.LocaleUtilKt;
import id.co.bri.brimo.util.RemoteConfigManager;
import android.os.Build;
import android.webkit.WebView;

import com.horcrux.svg.SvgPackage;
import com.learnium.RNDeviceInfo.RNDeviceInfo;
import com.reactnativecommunity.webview.RNCWebViewPackage;
import com.swmansion.rnscreens.RNScreensPackage;
import com.th3rdwave.safeareacontext.SafeAreaContextPackage;
import com.reactnativepagerview.PagerViewPackage;
import com.BV.LinearGradient.LinearGradientPackage;
import com.reactnativecommunity.clipboard.ClipboardPackage;
import com.brimodsdk.RNBrimodSDKPackage;

public class BaseApp extends Application implements ReactApplication {

    private ApplicationComponent mApplicationComponent;
    public BaseApp mApp;
    private RemoteConfigManager remoteConfigManager;
    private AppIconManager appIconManager; // Single instance for AppIconManager
    private String appIconKeyConfig, appIconKeyLocal;
    private static boolean isTrimMemoryCalled = false;

    // React Native Host
    private final ReactNativeHost mReactNativeHost = new ReactNativeHost(this) {
        @Override
        public boolean getUseDeveloperSupport() {
            return BuildConfig.DEBUG;
        }

        @Override
        protected List<ReactPackage> getPackages() {
            @SuppressWarnings("UnnecessaryLocalVariable")
            List<ReactPackage> packages = Arrays.<ReactPackage>asList(
                    new MainReactPackage(),
                    new SvgPackage(),
                    new RNScreensPackage(),
                    new SafeAreaContextPackage(),
                    new PagerViewPackage(),
                    new LinearGradientPackage(),
                    new ClipboardPackage(),
                    new RNBrimodSDKPackage(),
                    new RNCWebViewPackage(),
                    new LottiePackage(),
                     new ReactNativePushNotificationPackage(),
                    new RNDeviceInfo()
            );
            return packages;
        }

        @Override
        protected String getJSMainModuleName() {
            return "index";
        }

        @Override
        public String getJSBundleFile() {
            if (BrimodSDK.isForDemo()) {
                // OTA logic (sudah ada di kode kamu)
                DownloadManager downloadManager = new DownloadManager(BaseApp.this);
                java.io.File bundleFile = downloadManager.getBundleFile();

                if (bundleFile != null && bundleFile.exists() && bundleFile.length() > 1000) {
                    try (java.io.FileInputStream fis = new java.io.FileInputStream(bundleFile)) {
                        byte[] head = new byte[32];
                        int n = fis.read(head);
                        String headStr = new String(head, 0, n).toLowerCase();
                        if (!headStr.contains("<!doctype") && !headStr.contains("<html")) {
                            Log.i("OTA", "Using OTA bundle: " + bundleFile.getAbsolutePath());
                            return bundleFile.getAbsolutePath();
                        } else {
                            Log.e("OTA", "OTA bundle is HTML/error, fallback to asset");
                        }
                    } catch (Exception e) {
                        Log.e("OTA", "Error reading OTA bundle: " + e.getMessage());
                    }
                } else if (bundleFile != null && bundleFile.exists()) {
                    Log.e("OTA", "OTA bundle file too small or corrupt, fallback to asset");
                }
                Log.i("OTA", "Using default asset bundle");
            } else {
                // Mode development: return null agar RN pakai Metro bundler
                Log.i("OTA", "Development mode: using Metro bundler");
            }
            return null;
        }
    };

    @Override
    public ReactNativeHost getReactNativeHost() {
        return mReactNativeHost;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        mApp = this;
        isTrimMemoryCalled = false;

        // Initialize SoLoader for React Native
        SoLoader.init(this, /* native exopackage */ false);

        // Preload React Native engine
        getReactNativeHost().getReactInstanceManager().createReactContextInBackground();

        // inject Dagger 2
        // Application Module (context , shared preference)
        // API Module (Retrofit2)
        // ROOM Module (Dao Room)
        mApplicationComponent = DaggerApplicationComponent.builder()
                .applicationModule(new ApplicationModule(this))
                .apiModule(new ApiModule(this))
                .roomModule(new RoomModule(this))
                .minioModule(new MinioModule(this))
                .roomMenuModule(new RoomMenuModule(this))
                .roomRateModule(new RoomRateModule(this))
                .roomLifestyleModule(new RoomLifestyleModule(this))
                .roomPengelolaanKartuModule(new RoomPengelolaanKartuModule(this))
                .dynamicKeyModule(new DynamicKeyModule(this))
                .build();

        mApplicationComponent.inject(this);

        // Initialize AppsFlyer for analytics
        AppsFlyerUtil.initAppsflyer(getApplicationContext(), AppConfig.getAppsflyerKey(), this);

        // Initialize AppIconManager once
        appIconManager = new AppIconManager(this);

        // Setup default configurations
        setup();

        // Initialize Remote Config for fetching dynamic parameters
        initRemoteConfig();

        // Set up the delegate
        BrimodSDK delegate = new BrimodSDK(
            this,
            mApplicationComponent.apiSource(),
            mApplicationComponent.brimoPrefSource()
        );
        RNBrimodSDK.shared().setDelegate(delegate);

        // Initialize Payment Module Dependencies
        initPayment();
    }

    public void setup() {
        //set default language
        setLocale();

        //set default calendar
        AndroidThreeTen.init(this);

        //set default no night mode
        AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO);

        setupChuckerCollector();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P
                && Application.getProcessName() != null) {
            WebView.setDataDirectorySuffix(Application.getProcessName());
        }
    }

    private void setupChuckerCollector() {
        ChuckerCollector chuckerCollector = new ChuckerCollector(this);
        Chucker.registerDefaultCrashHandler(chuckerCollector);
    }

    /**
     * set locale language
     */
    public void setLocale() {
        String languageToLoad = LocaleUtilKt.getSavedLanguage(this);
        Locale locale = new Locale(languageToLoad);
        Locale.setDefault(locale);
        Configuration config = new Configuration();
        config.setLocale(locale);
        getResources().updateConfiguration(config,
                getBaseContext().getResources().getDisplayMetrics());
    }

    public ApplicationComponent getComponent() {
        return mApplicationComponent;
    }

    // Needed to replace the component with a test specific one
    public void setComponent(ApplicationComponent applicationComponent) {
        mApplicationComponent = applicationComponent;
    }

    @Override
    protected void attachBaseContext(Context base) {
        // Apply the saved language preference for localization
        String newLang = LocaleUtilKt.getSavedLanguage(base);
        super.attachBaseContext(LocaleUtilKt.applyLanguageContext(base, newLang));
    }

    private void initRemoteConfig() {
        // Initialize RemoteConfigManager to fetch dynamic configurations
        remoteConfigManager = RemoteConfigManager.Companion.getInstance(this);

        // Fetch and activate remote configurations
        remoteConfigManager.fetchAndActivate(new RemoteConfigManager.RemoteConfigFetchListener() {
            @Override
            public void onFetchComplete() {
                // Save the app icon key retrieved from remote config
                saveAppIconKey();
            }

            @Override
            public void onFetchFailed() {
                // Handle fetch failure (log if necessary)
            }
        });
    }

    private void saveAppIconKey() {
        // Retrieve the dynamic app icon key from remote config
        String iconKey = remoteConfigManager.getString(Constant.RemoteConfigParameter.DYNAMIC_APP_ICON);
        appIconKeyConfig = iconKey;

        // Save the icon key locally and update the app icon
        appIconManager.saveAppIconKey(this, iconKey);
    }

    @Override
    public void onTrimMemory(int level) {
        super.onTrimMemory(level);

        // Handle memory trimming when the app goes to the background
        if (!isTrimMemoryCalled && (level == TRIM_MEMORY_UI_HIDDEN || level == TRIM_MEMORY_BACKGROUND)) {
            appIconKeyLocal = appIconManager.getAppIconKey(this);
            appIconManager.updateAppIcon(appIconKeyLocal);
        }
        isTrimMemoryCalled = true;
    }

    private void initPayment() {
        PaymentDependency.INSTANCE.setPaymentApi(
                new PaymentImpl(
                        mApplicationComponent.apiService(),
                        mApplicationComponent.brimoPrefSource(),
                        mApplicationComponent.gson(),
                        mApplicationComponent.apiSource()
                )
        );
    }
}