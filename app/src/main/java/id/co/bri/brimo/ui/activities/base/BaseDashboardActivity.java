package id.co.bri.brimo.ui.activities.base;

import android.annotation.SuppressLint;
import android.content.IntentFilter;
import android.graphics.Color;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.FrameLayout;


import com.google.android.material.bottomnavigation.BottomNavigationItemView;
import com.google.android.material.bottomnavigation.BottomNavigationMenuView;
import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.google.android.material.bottomnavigation.LabelVisibilityMode;

import id.co.bri.brimo.R;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.domain.config.Constant;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import butterknife.Bind;
import butterknife.ButterKnife;
import id.co.bri.brimo.domain.helpers.GeneralHelperNewSkin;
import id.co.bri.brimo.domain.helpers.rooted.CekRootHelper;
import id.co.bri.brimo.domain.helpers.rooted.TamperingProtection;
import id.co.bri.brimo.security.MyCryptStatic;

public abstract class BaseDashboardActivity extends NewSkinBaseActivity {

    private static final String TAG = "BaseDashboardActivity";

    @Nullable
    @Bind(R.id.fragmentDashboardIB)
    public FrameLayout content; //FrameLayout untuk dashboard

    @Nullable
    @Bind(R.id.navigation)
    public BottomNavigationView bottomNavigationView; //navigatioan Bar

    protected int currentPos = -1;
    protected boolean isHome = true;
    protected List<Fragment> fragments = new ArrayList<>();


    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        setContentView(getLayoutResource());

        ButterKnife.bind(this);

        //remove all fragment
        clearBackStack();

        //remove efek zoom di navigation bar
        removeShiftMode(bottomNavigationView);

        //initiate Navigation Bar
        initiateNavBar();

        //register broadcast
        LocalBroadcastManager
                .getInstance(this)
                .registerReceiver(updates_receiver, new IntentFilter(Constant.TAG_NOTIF));

//        integrityCheck2();

    }

    public abstract int getLayoutResource();

    protected abstract void initiateNavBar();

    /*
     * Method untuk mereset susunan fragment
     */
    protected void clearBackStack() {
        FragmentManager manager = getSupportFragmentManager();
        manager.popBackStack(null, FragmentManager.POP_BACK_STACK_INCLUSIVE);
    }


    /*
     * Method untuk mengconfig Navigation Menu
     */
    @SuppressLint({"RestrictedApi", "WrongConstant"})
    protected void removeShiftMode(BottomNavigationView view) {
        BottomNavigationMenuView menuView = (BottomNavigationMenuView) view.getChildAt(0);
        try {
            Field shiftingMode = menuView.getClass().getDeclaredField("mShiftingMode");
            shiftingMode.setAccessible(true);
            shiftingMode.setBoolean(menuView, false);
            shiftingMode.setAccessible(false);

            for (int i = 0; i < menuView.getChildCount(); i++) {
                BottomNavigationItemView item = (BottomNavigationItemView) menuView.getChildAt(i);
                item.setLabelVisibilityMode(LabelVisibilityMode.LABEL_VISIBILITY_LABELED);

                // set once again checked value, so view will be updated
                item.setChecked(item.getItemData().isChecked());
            }

        } catch (NoSuchFieldException e) {
            if(!GeneralHelper.isProd()) {
                Log.e("ERROR NO SUCH FIELD", "Unable to get shift mode field");
            }
        } catch (IllegalAccessException e) {
            if(!GeneralHelper.isProd()) {
                Log.e("ERROR ILLEGAL ALG", "Unable to change value of shift mode");
            }
        }
    }


    /*
     * Digunakan untuk transisi dashboard fragment
     */
    protected void switchFragment(int pos, String tag) {

        if (fragments.isEmpty()) return;

        // Do nothing if click same page
        if (currentPos == pos) return;

        // Initialize
        if (currentPos == -1) {
            getSupportFragmentManager()
                    .beginTransaction()
                    .setCustomAnimations(R.anim.fade_in_trasition, R.anim.fade_out_trasition)
                    .replace(R.id.fragmentDashboardIB, fragments.get(pos), tag)
                    .commit();
            return;
        }

        if (getSupportFragmentManager().findFragmentByTag(tag) == null || !getSupportFragmentManager().findFragmentByTag(tag).isAdded()) {
            getSupportFragmentManager()
                    .beginTransaction()
                    .setCustomAnimations(R.anim.fade_in_trasition, R.anim.fade_out_trasition)
                    .hide(fragments.get(currentPos))
                    .add(R.id.fragmentDashboardIB, fragments.get(pos), tag)
                    .commit();
        } else {
            getSupportFragmentManager()
                    .beginTransaction()
                    .setCustomAnimations(R.anim.fade_in_trasition, R.anim.fade_out_trasition)
                    .hide(fragments.get(currentPos))
                    .show(fragments.get(pos))
                    .commit();
        }

        currentPos = pos;
        if (currentPos <= 0) {
            isHome = true;
        } else {
            isHome = false;
        }
    }


    /**
     * Method untuk melakukan pengecekan TEMPERING CODE
     */
    private void integrityCheck2() {

        if (AppConfig.ROOT_CHECKING) {
            long dexCrc = 0;
            try {
                dexCrc = Long.parseLong(MyCryptStatic.decryptAsBase64(this.getResources().getString(R.string.crc_dex))); // Keep dexCrc in resources (strings.xml) or in JNI code. Not hardcode in java classes.
            } catch (Exception e) {
                if (!GeneralHelper.isProd()) {
                    Log.e(TAG, "maxProtectionTempering: ", e);
                }
            }

            TamperingProtection protection = new TamperingProtection(this);

            protection.setAcceptedDexCrcs(dexCrc);
            protection.setAcceptStartOnEmulator(false);
            protection.setAcceptStartInDebugMode(false);

            try {
                protection.setAcceptedPackageNames(MyCryptStatic.decryptAsBase64(AppConfig.STRING_PACKAGE));
                protection.setAcceptedSignatures(MyCryptStatic.decryptAsBase64(AppConfig.STRING_KEY_STORE));
            } catch (Exception e) {
                if (!GeneralHelper.isProd()) {
                    Log.e(TAG, "maxProtectionTempering: ", e);
                }
            }

            try {
                protection.validateAllOrThrowException();
            } catch (TamperingProtection.ValidationException e) {
                if (!GeneralHelper.isProd()) {
                    Log.e(TAG, "maxProtectionTempering: ", e);
                }

                finishAffinity();
            }
        }
    }

    @Override
    public void showProgress() {
        GeneralHelperNewSkin.INSTANCE.showLoadingDialog(this);
    }

    @Override
    public void hideProgress() {
        GeneralHelperNewSkin.INSTANCE.dismissLoadingDialog();
    }

    public void setupTransparentStatusBar() {
        Window window = getWindow();
        window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
        window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
        window.getDecorView().setSystemUiVisibility(
                View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN | View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR
        );
        window.setStatusBarColor(Color.TRANSPARENT);
    }
}
