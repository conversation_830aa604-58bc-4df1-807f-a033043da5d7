package id.co.bri.brimo.ui.fragments;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.content.Context;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.facebook.react.ReactInstanceManager;
import com.facebook.react.ReactRootView;

import id.co.bri.brimo.BaseApp;
import id.co.bri.brimo.R;

/**
 * Fragment for displaying a React Native component inside an Android app.
 * Handles lifecycle integration and property passing for React Native modules.
 */
public class ReactNativeFragment extends Fragment {
    private static final String ARG_MODULE_NAME = "module_name";
    private static final String ARG_INITIAL_PROPS = "initial_props";

    private ReactRootView mReactRootView;
    private ReactInstanceManager mReactInstanceManager;
    private String mModuleName;
    private Bundle mInitialProps;
    private boolean isReloading = false;

    /**
     * Factory method to create a new instance of ReactNativeFragment.
     *
     * @param moduleName   The name of the React Native module to load.
     * @param initialProps Optional initial props to pass to the React Native component.
     * @return A new instance of ReactNativeFragment.
     */
    public static ReactNativeFragment newInstance(@NonNull String moduleName, @Nullable Bundle initialProps) {
        ReactNativeFragment fragment = new ReactNativeFragment();
        Bundle args = new Bundle();
        args.putString(ARG_MODULE_NAME, moduleName);
        Log.i("ReactNativeFragment", moduleName);
        if (initialProps != null) {
            Log.i("ReactNativeFragment", initialProps.toString());
            args.putBundle(ARG_INITIAL_PROPS, initialProps);
        }
        fragment.setArguments(args);
        return fragment;
    }

    /**
     * Called to do initial creation of the fragment.
     * Retrieves module name and initial props from arguments.
     */
    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getArguments() != null) {
            mModuleName = getArguments().getString(ARG_MODULE_NAME);
            mInitialProps = getArguments().getBundle(ARG_INITIAL_PROPS);
        }

        // Validate moduleName is not null
        if (mModuleName == null || mModuleName.isEmpty()) {
            throw new IllegalArgumentException("moduleName cannot be null or empty");
        }
    }

    /**
     * Called to have the fragment instantiate its user interface view.
     * Sets up the ReactRootView and starts the React Native application.
     */
    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater,
                             @Nullable ViewGroup container,
                             @Nullable Bundle savedInstanceState) {
        Context context = new android.view.ContextThemeWrapper(requireContext(), R.style.ReactNativeAppTheme);
        Log.d("ReactNativeFragment", "onCreateView context: " + context);
        mReactRootView = new ReactRootView(context);
        Log.d("ReactNativeFragment", "onCreateView mReactRootView.getContext(): " + mReactRootView.getContext());

        mReactInstanceManager = ((BaseApp) requireActivity().getApplication())
                .getReactNativeHost()
                .getReactInstanceManager();

        // Use provided initialProps or create empty bundle if null
        Bundle propsToUse = mInitialProps != null ? mInitialProps : new Bundle();

        mReactRootView.startReactApplication(
                mReactInstanceManager,
                mModuleName,
                propsToUse
        );

        return mReactRootView;
    }

    /**
     * Called when the Fragment is no longer resumed.
     * Notifies the ReactInstanceManager of the host pause event.
     */
    @Override
    public void onPause() {
        super.onPause();
        Log.d("ReactNativeFragment", "onPause mReactRootView.getContext(): " + (mReactRootView != null ? mReactRootView.getContext() : "null"));
        mReactInstanceManager.onHostPause(requireActivity());
    }

    /**
     * Called when the Fragment is visible to the user and actively running.
     * Notifies the ReactInstanceManager of the host resume event.
     */
    @Override
    public void onResume() {
        super.onResume();
        Log.d("ReactNativeFragment", "onResume mReactRootView.getContext(): " + (mReactRootView != null ? mReactRootView.getContext() : "null"));
        mReactInstanceManager.onHostResume(requireActivity(), null);
    }

    /**
     * Called when the Fragment is no longer in use.
     * Notifies the ReactInstanceManager of the host destroy event.
     */
    @Override
    public void onDestroy() {
        super.onDestroy();
        Log.d("ReactNativeFragment", "onDestroy mReactRootView.getContext(): " + (mReactRootView != null ? mReactRootView.getContext() : "null"));
        mReactInstanceManager.onHostDestroy(requireActivity());
    }

    /**
     * Called when the view hierarchy associated with the fragment is being removed.
     * Unmounts the React application from the ReactRootView.
     */
    @Override
    public void onDestroyView() {
        super.onDestroyView();
        Log.d("ReactNativeFragment", "onDestroyView mReactRootView.getContext(): " + (mReactRootView != null ? mReactRootView.getContext() : "null"));
        if (mReactRootView != null) {
            mReactRootView.unmountReactApplication();
            mReactRootView = null;
        }
    }
}