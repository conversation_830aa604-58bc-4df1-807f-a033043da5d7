package id.co.bri.brimo.models.apimodel.response;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class LoginResponse {

    @SerializedName("token_key")
    @Expose
    private String tokenKey;
    @SerializedName("otp_expired_in_second")
    @Expose
    private Integer otpExpiredInSecond;
    @SerializedName("is_pin_ready")
    @Expose
    private boolean isPinReady;
    @SerializedName("reference_number")
    @Expose
    private String refNum;
    @SerializedName("nickname")
    @Expose
    private String nickname;
    @SerializedName("full_name")
    @Expose
    private String fullName;
    @SerializedName("type")
    @Expose
    private String type;
    @SerializedName("method")
    @Expose
    private String method;
    @SerializedName("phone")
    @Expose
    private String phone;
    @SerializedName("username")
    @Expose
    private String username;

    public String getTokenKey() {
        return tokenKey;
    }

    public void setTokenKey(String tokenKey) {
        this.tokenKey = tokenKey;
    }

    public Integer getOtpExpiredInSecond() {
        return otpExpiredInSecond;
    }

    public void setOtpExpiredInSecond(Integer otpExpiredInSecond) {
        this.otpExpiredInSecond = otpExpiredInSecond;
    }

    public boolean isPinReady() {
        return isPinReady;
    }

    public void setPinReady(boolean pinReady) {
        isPinReady = pinReady;
    }

    public String getRefNum() {
        return refNum;
    }

    public void setRefNum(String refNum) {
        this.refNum = refNum;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }
}