package id.co.bri.brimo.contract.IPresenter.asuransi;

import id.co.bri.brimo.contract.IPresenter.base.IBaseFormPresenter;
import id.co.bri.brimo.contract.IView.base.IBaseFormView;
import id.co.bri.brimo.models.apimodel.request.InquiryAsuransiRequest;

public interface IFormAsuransiPresenter<V extends IBaseFormView>
        extends IBaseFormPresenter<V> {

    void getDataInquiry(InquiryAsuransiRequest request, boolean isFromFastmenu);
}
